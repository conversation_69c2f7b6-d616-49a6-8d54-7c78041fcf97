import { Transform, Type } from "class-transformer";
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";
import { RecurrenceRuleDto } from "./bulk-create-course-events.dto";

export class CreateCourseEventDto {
  @IsArray()
  @IsArray({ each: true })
  category: string[][];

  @IsString()
  name: string;

  @Transform(toMongoObjectId)
  teacher: Types.ObjectId;

  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsString()
  start_date_str: string;

  @IsString()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  hours: number;

  @IsString()
  @IsOptional()
  zoom_account: string;

  @IsString()
  @IsOptional()
  note: string;

  @IsArray()
  @IsOptional()
  department_ids: number[];

  @Transform(toMongoObjectId)
  @IsOptional()
  check_user: Types.ObjectId;

  @IsString()
  @IsOptional()
  work_content: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => RecurrenceRuleDto)
  recurrence_rule?: RecurrenceRuleDto;
}
