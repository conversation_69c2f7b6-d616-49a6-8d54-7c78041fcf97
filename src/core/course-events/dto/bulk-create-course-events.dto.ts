import { Transform, Type } from "class-transformer";
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested, IsIn, IsObject } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";
import { RecurrenceRule, TimeSlot } from "../course-event.model";

export class CourseEventDateDto {
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsString()
  start_date_str: string;

  @IsString()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  hours: number;
}

export class TimeSlotDto implements TimeSlot {
  @IsString()
  start_time: string; // HH:mm format

  @IsString()
  end_time: string; // HH:mm format
}

export class RecurrenceRuleDto implements RecurrenceRule {
  @IsString()
  @IsIn(['daily', 'weekly', 'monthly', 'yearly'])
  recurrence_type: 'daily' | 'weekly' | 'monthly' | 'yearly';

  @IsNumber()
  @Transform(({ value }) => Number(value))
  recurrence_interval: number;

  @IsString()
  @IsIn(['count', 'date'])
  end_condition: 'count' | 'date';

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  occurrence_count?: number;

  @IsOptional()
  @IsString()
  end_date?: string; // YYYY-MM-DD format

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  week_days?: number[]; // For weekly recurrence [0-6, where 0=Sunday]

  // Enhanced weekly time slots - supports multiple time slots per weekday
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  week_time_slots?: { [weekday: number]: TimeSlotDto[] }; // New: Multiple time slots per weekday

  // Legacy single time slot support (for backward compatibility)
  @IsOptional()
  @IsString()
  start_time?: string; // HH:mm format - deprecated, use week_time_slots for weekly

  @IsOptional()
  @IsString()
  end_time?: string; // HH:mm format - deprecated, use week_time_slots for weekly

  @IsOptional()
  @IsObject()
  week_times_start?: { [weekday: number]: string }; // Legacy format

  @IsOptional()
  @IsObject()
  week_times_end?: { [weekday: number]: string }; // Legacy format

  @IsString()
  first_date: string; // YYYY-MM-DD format
}

export class BulkCreateCourseEventsDto {
  @IsArray()
  @IsArray({ each: true })
  category: string[][];

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  work_content: string;

  @Transform(toMongoObjectId)
  teacher: Types.ObjectId;

  @Transform(toMongoObjectId)
  check_user: Types.ObjectId;

  @IsArray()
  department_ids: number[];

  @IsString()
  @IsOptional()
  note: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CourseEventDateDto)
  course_dates: CourseEventDateDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => RecurrenceRuleDto)
  recurrence_rule?: RecurrenceRuleDto;
}
