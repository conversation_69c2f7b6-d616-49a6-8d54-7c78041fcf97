import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON><PERSON>y, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";
import { RecurrenceRuleDto, TimeSlotDto } from "./bulk-create-course-events.dto";

export class UpdateCourseEventDto {
  @IsArray()
  @IsArray({ each: true })
  @IsOptional()
  category: string[][];

  @IsString()
  @IsOptional()
  name: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;

  @IsString()
  @IsOptional()
  zoom_account: string;

  @IsString()
  @IsOptional()
  note: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => RecurrenceRuleDto)
  recurrence_rule?: RecurrenceRuleDto;
}