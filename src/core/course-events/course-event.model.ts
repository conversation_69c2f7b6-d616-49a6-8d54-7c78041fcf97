import { DocumentType, Ref, prop } from "@typegoose/typegoose";
import { User } from "../users/user.model";
import { Types } from "mongoose";

export type CourseEventDocument = DocumentType<CourseEvent> & Document;

// 重复规则接口
export interface RecurrenceRule {
  recurrence_type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurrence_interval: number;
  end_condition: 'count' | 'date';
  occurrence_count?: number;
  end_date?: string; // YYYY-MM-DD format
  week_days?: number[]; // For weekly recurrence [0-6, where 0=Sunday]
  start_time: string; // HH:mm format
  end_time: string; // HH:mm format
  first_date: string; // YYYY-MM-DD format
}

export class CourseEvent {
  // @prop()
  // public _id: Types.ObjectId;
  
  // 课程属性 - 支持多个分类，每个分类可以有多个子项
  @prop()
  public category: string[][];

  // 课程状态：0未完成1已完成
  @prop({default: 0})
  public status: number;

  // 课程名称
  @prop()
  public name: string;

  // 讲师姓名
  @prop({ ref: 'User' })
  public teacher?: Ref<User>;

  // 上课日期
  @prop()
  public date: Date;

  // 开始时间
  @prop()
  public start_date_str: string;
 
  // 结束时间
  @prop()
  public end_date_str: string;

  // 持续时间
  @prop()
  public hours: number;

  // zoom账号
  @prop()
  public zoom_account: string;

  // 备注
  @prop()
  public note: string;

  // 部门
  @prop()
  public department_ids: number[];

  // 审核人
  @prop({ ref: 'User' })
  public check_user?: Ref<User>;

  // 课程内容
  @prop()
  public work_content: string;

  // 通知状态
  @prop({ default: false })
  public has_notification: boolean;

  // 重复规则 - 存储课程事件的重复规则元数据
  @prop({ type: Object })
  public recurrence_rule?: RecurrenceRule;
}